package com.youying.system.domain.userreminder;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 用户提醒设置请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class UserReminderRequest {

    /**
     * 发薪日提醒是否启用(0:关闭,1:开启)
     */
    private Integer salaryDayEnabled;

    /**
     * 发薪日(1-31)
     */
    @Min(value = 1, message = "发薪日必须在1-31之间")
    @Max(value = 31, message = "发薪日必须在1-31之间")
    private Integer salaryDay;

    /**
     * 发薪日提前提醒天数
     */
    @Min(value = 0, message = "提前提醒天数不能小于0")
    @Max(value = 30, message = "提前提醒天数不能超过30天")
    private Integer salaryDayAdvanceDays;

    /**
     * 开演时间提醒是否启用(0:关闭,1:开启)
     */
    private Integer showTimeEnabled;

    /**
     * 开演时间提前提醒小时数
     */
    @Min(value = 0, message = "提前提醒小时数不能小于0")
    @Max(value = 168, message = "提前提醒小时数不能超过168小时(7天)")
    private Integer showTimeAdvanceHours;

    /**
     * 开票时间提醒是否启用(0:关闭,1:开启)
     */
    private Integer ticketSaleEnabled;

    /**
     * 开票时间提前提醒小时数
     */
    @Min(value = 0, message = "提前提醒小时数不能小于0")
    @Max(value = 720, message = "提前提醒小时数不能超过720小时(30天)")
    private Integer ticketSaleAdvanceHours;

    /**
     * 看剧基金提醒是否启用(0:关闭,1:开启)
     */
    private Integer theaterFundEnabled;

    /**
     * 看剧基金当前金额
     */
    @DecimalMin(value = "0.00", message = "当前金额不能小于0")
    @DecimalMax(value = "999999.99", message = "当前金额不能超过999999.99")
    private BigDecimal theaterFundCurrentAmount;

    /**
     * 看剧基金目标金额
     */
    @DecimalMin(value = "0.00", message = "目标金额不能小于0")
    @DecimalMax(value = "999999.99", message = "目标金额不能超过999999.99")
    private BigDecimal theaterFundTargetAmount;

    /**
     * 看剧基金提醒阈值(0.1-1.0)
     */
    @DecimalMin(value = "0.10", message = "提醒阈值不能小于0.10")
    @DecimalMax(value = "1.00", message = "提醒阈值不能大于1.00")
    private BigDecimal theaterFundReminderThreshold;

    /**
     * 备注
     */
    private String remark;
}
