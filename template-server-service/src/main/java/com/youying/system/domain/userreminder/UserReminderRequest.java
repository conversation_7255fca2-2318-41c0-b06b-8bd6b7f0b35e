package com.youying.system.domain.userreminder;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 用户提醒设置请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class UserReminderRequest {

    /**
     * 发薪日(1-31)
     */
    @Min(value = 1, message = "发薪日必须在1-31之间")
    @Max(value = 31, message = "发薪日必须在1-31之间")
    private Integer salaryDay;

    /**
     * 开演时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ticketSaleTime;

    /**
     * 看剧基金当前金额
     */
    @DecimalMin(value = "0.00", message = "当前金额不能小于0")
    @DecimalMax(value = "999999.99", message = "当前金额不能超过999999.99")
    private BigDecimal theaterFundCurrentAmount;

    /**
     * 看剧基金目标金额
     */
    @DecimalMin(value = "0.00", message = "目标金额不能小于0")
    @DecimalMax(value = "999999.99", message = "目标金额不能超过999999.99")
    private BigDecimal theaterFundTargetAmount;
}
