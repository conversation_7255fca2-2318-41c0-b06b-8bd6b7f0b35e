package com.youying.system.domain.userreminder;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 用户提醒设置响应DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class UserReminderResponse {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 发薪日(1-31)
     */
    private Integer salaryDay;

    /**
     * 开演时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ticketSaleTime;

    /**
     * 看剧基金当前金额
     */
    private BigDecimal theaterFundCurrentAmount;

    /**
     * 看剧基金目标金额
     */
    private BigDecimal theaterFundTargetAmount;

    /**
     * 看剧基金完成进度百分比
     */
    private BigDecimal progressPercentage;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
