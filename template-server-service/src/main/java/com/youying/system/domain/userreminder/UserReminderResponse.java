package com.youying.system.domain.userreminder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户提醒设置响应DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class UserReminderResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 发薪日提醒设置
     */
    private SalaryDayReminder salaryDayReminder;

    /**
     * 开演时间提醒设置
     */
    private ShowTimeReminder showTimeReminder;

    /**
     * 开票时间提醒设置
     */
    private TicketSaleReminder ticketSaleReminder;

    /**
     * 看剧基金提醒设置
     */
    private TheaterFundReminder theaterFundReminder;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发薪日提醒设置
     */
    @Data
    public static class SalaryDayReminder {
        /**
         * 是否启用(0:关闭,1:开启)
         */
        private Integer enabled;

        /**
         * 发薪日(1-31)
         */
        private Integer salaryDay;

        /**
         * 提前提醒天数
         */
        private Integer advanceDays;
    }

    /**
     * 开演时间提醒设置
     */
    @Data
    public static class ShowTimeReminder {
        /**
         * 是否启用(0:关闭,1:开启)
         */
        private Integer enabled;

        /**
         * 提前提醒小时数
         */
        private Integer advanceHours;
    }

    /**
     * 开票时间提醒设置
     */
    @Data
    public static class TicketSaleReminder {
        /**
         * 是否启用(0:关闭,1:开启)
         */
        private Integer enabled;

        /**
         * 提前提醒小时数
         */
        private Integer advanceHours;
    }

    /**
     * 看剧基金提醒设置
     */
    @Data
    public static class TheaterFundReminder {
        /**
         * 是否启用(0:关闭,1:开启)
         */
        private Integer enabled;

        /**
         * 当前金额
         */
        private BigDecimal currentAmount;

        /**
         * 目标金额
         */
        private BigDecimal targetAmount;

        /**
         * 提醒阈值
         */
        private BigDecimal reminderThreshold;

        /**
         * 完成进度百分比
         */
        private BigDecimal progressPercentage;

        /**
         * 是否达到提醒阈值
         */
        private Boolean reachedThreshold;
    }
}
