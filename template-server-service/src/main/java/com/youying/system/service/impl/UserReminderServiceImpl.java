package com.youying.system.service.impl;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.mapper.UserReminderMapper;
import com.youying.system.service.UserReminderService;

/**
 * <p>
 * 用户提醒设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Service
public class UserReminderServiceImpl extends ServiceImpl<UserReminderMapper, UserReminder>
        implements UserReminderService {

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    @Override
    public UserReminder findByUserId(Integer userId) {
        UserReminder userReminder = baseMapper.findByUserId(userId);
        if (userReminder == null) {
            // 如果用户没有设置，返回空对象
            userReminder = new UserReminder();
            userReminder.setUserId(userId);
        }
        return userReminder;
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param userReminder 用户提醒设置
     * @return 是否成功
     */
    @Override
    public boolean saveOrUpdateUserReminder(UserReminder userReminder) {
        if (userReminder.getUserId() == null) {
            return false;
        }

        // 查询是否已存在
        UserReminder existingReminder = baseMapper.findByUserId(userReminder.getUserId());

        if (existingReminder != null) {
            // 更新现有记录
            userReminder.setId(existingReminder.getId());
            return updateById(userReminder);
        } else {
            // 创建新记录
            userReminder.setCreateTime(new Date());
            return save(userReminder);
        }
    }
}
