package com.youying.system.service.impl;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.mapper.UserReminderMapper;
import com.youying.system.service.UserReminderService;

/**
 * <p>
 * 用户提醒设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Service
public class UserReminderServiceImpl extends ServiceImpl<UserReminderMapper, UserReminder>
        implements UserReminderService {

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    @Override
    public UserReminder findByUserId(Long userId) {
        UserReminder userReminder = baseMapper.findByUserId(userId);
        if (userReminder == null) {
            // 如果用户没有设置，返回默认设置
            userReminder = initUserReminder(userId);
        }
        return userReminder;
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param userReminder 用户提醒设置
     * @return 是否成功
     */
    @Override
    public boolean saveOrUpdateUserReminder(UserReminder userReminder) {
        if (userReminder.getUserId() == null) {
            return false;
        }

        // 查询是否已存在
        UserReminder existingReminder = baseMapper.findByUserId(userReminder.getUserId());

        if (existingReminder != null) {
            // 更新现有记录
            userReminder.setId(existingReminder.getId());
            userReminder.setUpdateTime(new Date());
            return updateById(userReminder);
        } else {
            // 创建新记录
            userReminder.setCreateTime(new Date());
            userReminder.setUpdateTime(new Date());
            userReminder.setStatus(1);
            userReminder.setDeleted(1);
            return save(userReminder);
        }
    }

    /**
     * 根据用户ID删除用户提醒设置
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    public boolean deleteByUserId(Long userId) {
        return baseMapper.deleteByUserId(userId) > 0;
    }

    /**
     * 初始化用户提醒设置（使用默认值）
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    @Override
    public UserReminder initUserReminder(Long userId) {
        UserReminder userReminder = new UserReminder();
        userReminder.setUserId(userId);

        // 发薪日提醒默认设置
        userReminder.setSalaryDayEnabled(0);
        userReminder.setSalaryDay(15); // 默认15号发薪
        userReminder.setSalaryDayAdvanceDays(0);

        // 开演时间提醒默认设置
        userReminder.setShowTimeEnabled(0);
        userReminder.setShowTimeAdvanceHours(2); // 默认提前2小时

        // 开票时间提醒默认设置
        userReminder.setTicketSaleEnabled(0);
        userReminder.setTicketSaleAdvanceHours(24); // 默认提前24小时

        // 看剧基金提醒默认设置
        userReminder.setTheaterFundEnabled(0);
        userReminder.setTheaterFundCurrentAmount(BigDecimal.ZERO);
        userReminder.setTheaterFundTargetAmount(BigDecimal.ZERO);
        userReminder.setTheaterFundReminderThreshold(new BigDecimal("0.80")); // 默认80%

        userReminder.setStatus(1);
        userReminder.setDeleted(1);

        return userReminder;
    }
}
