package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserReminder;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户提醒设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface UserReminderMapper extends BaseMapper<UserReminder> {

    /**
     * 根据用户ID查询用户提醒设置
     *
     * @param userId 用户ID
     * @return 用户提醒设置
     */
    UserReminder findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID删除用户提醒设置
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
