<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserReminderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserReminder">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="salary_day_enabled" property="salaryDayEnabled"/>
        <result column="salary_day" property="salaryDay"/>
        <result column="salary_day_advance_days" property="salaryDayAdvanceDays"/>
        <result column="show_time_enabled" property="showTimeEnabled"/>
        <result column="show_time_advance_hours" property="showTimeAdvanceHours"/>
        <result column="ticket_sale_enabled" property="ticketSaleEnabled"/>
        <result column="ticket_sale_advance_hours" property="ticketSaleAdvanceHours"/>
        <result column="theater_fund_enabled" property="theaterFundEnabled"/>
        <result column="theater_fund_current_amount" property="theaterFundCurrentAmount"/>
        <result column="theater_fund_target_amount" property="theaterFundTargetAmount"/>
        <result column="theater_fund_reminder_threshold" property="theaterFundReminderThreshold"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, salary_day_enabled, salary_day, salary_day_advance_days,
        show_time_enabled, show_time_advance_hours, ticket_sale_enabled, ticket_sale_advance_hours,
        theater_fund_enabled, theater_fund_current_amount, theater_fund_target_amount, theater_fund_reminder_threshold,
        status, deleted, create_by, create_time, update_by, update_time, remark
    </sql>

    <!-- 根据用户ID查询用户提醒设置 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_user_reminder
        WHERE user_id = #{userId}
        AND deleted = 1
        LIMIT 1
    </select>

    <!-- 根据用户ID删除用户提醒设置 -->
    <update id="deleteByUserId">
        UPDATE t_user_reminder
        SET deleted = 0,
            update_time = NOW()
        WHERE user_id = #{userId}
        AND deleted = 1
    </update>

</mapper>
