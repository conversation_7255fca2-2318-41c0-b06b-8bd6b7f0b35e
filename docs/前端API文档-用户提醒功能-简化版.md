# 用户提醒功能 - 前端API文档（简化版）

## 📋 功能概述

用户提醒功能允许用户设置四种类型的提醒：
- 💰 **发薪日提醒**：设置发薪日期（1-31号）
- 🎭 **开演时间提醒**：设置具体的演出开始时间
- 🎫 **开票时间提醒**：设置具体的开票时间  
- 💳 **看剧基金提醒**：管理看剧资金的当前金额和目标金额

---

## 🔗 API接口列表

### 1. 获取用户提醒设置

**接口信息**
```
GET /userReminder/getUserReminder
```

**请求示例**
```javascript
const { data } = await axios.get('/userReminder/getUserReminder');
```

**响应数据结构**
```typescript
interface UserReminderResponse {
  id: number;
  userId: number;
  salaryDay: number;              // 发薪日 1-31
  showTime: string;               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime: string;         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundCurrentAmount: number; // 看剧基金当前金额
  theaterFundTargetAmount: number;  // 看剧基金目标金额
  progressPercentage: number;       // 完成进度百分比
  createTime: string;
}
```

**响应示例**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "userId": 123,
    "salaryDay": 15,
    "showTime": "2025-02-15 19:30:00",
    "ticketSaleTime": "2025-01-15 10:00:00",
    "theaterFundCurrentAmount": 500.00,
    "theaterFundTargetAmount": 1000.00,
    "progressPercentage": 50.00,
    "createTime": "2025-01-03 10:00:00"
  }
}
```

---

### 2. 保存/更新用户提醒设置

**接口信息**
```
POST /userReminder/saveOrUpdateUserReminder
```

**请求参数类型**
```typescript
interface UserReminderRequest {
  salaryDay?: number;              // 发薪日 1-31
  showTime?: string;               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime?: string;         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundCurrentAmount?: number; // 看剧基金当前金额 0.00-999999.99
  theaterFundTargetAmount?: number;  // 看剧基金目标金额 0.00-999999.99
}
```

**请求示例**
```javascript
const requestData = {
  salaryDay: 15,
  showTime: "2025-02-15 19:30:00",
  ticketSaleTime: "2025-01-15 10:00:00",
  theaterFundCurrentAmount: 500.00,
  theaterFundTargetAmount: 1000.00
};

const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
});
```

**响应示例**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

### 3. 删除用户提醒设置

**接口信息**
```
DELETE /userReminder/deleteUserReminder
```

**请求示例**
```javascript
const response = await fetch('/userReminder/deleteUserReminder', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

---

### 4. 更新看剧基金金额

**接口信息**
```
POST /userReminder/updateTheaterFund
```

**请求参数**
- `currentAmount` (必填): 当前金额
- `targetAmount` (可选): 目标金额

**请求示例**
```javascript
// 只更新当前金额
const response1 = await fetch('/userReminder/updateTheaterFund?currentAmount=600.00', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});

// 同时更新当前金额和目标金额
const params = new URLSearchParams({
  currentAmount: '600.00',
  targetAmount: '1200.00'
});

const response2 = await fetch(`/userReminder/updateTheaterFund?${params}`, {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

---

## 🎨 前端实现建议

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';

export const useUserReminder = () => {
  const [data, setData] = useState<UserReminderResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const refresh = async () => {
    setLoading(true);
    try {
      const response = await fetch('/userReminder/getUserReminder');
      const result = await response.json();
      if (result.code === 200) {
        setData(result.data);
      }
    } catch (err) {
      console.error('获取提醒设置失败', err);
    } finally {
      setLoading(false);
    }
  };

  const save = async (requestData: UserReminderRequest): Promise<boolean> => {
    try {
      const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('保存失败', err);
      return false;
    }
  };

  const updateFund = async (current: number, target?: number): Promise<boolean> => {
    try {
      const params = new URLSearchParams({ currentAmount: current.toString() });
      if (target !== undefined) {
        params.append('targetAmount', target.toString());
      }
      
      const response = await fetch(`/userReminder/updateTheaterFund?${params}`, {
        method: 'POST'
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('更新失败', err);
      return false;
    }
  };

  useEffect(() => {
    refresh();
  }, []);

  return { data, loading, refresh, save, updateFund };
};
```

---

## ⚠️ 注意事项

### 参数验证规则
- `salaryDay`: 1-31之间的整数
- `showTime/ticketSaleTime`: 标准时间格式 "yyyy-MM-dd HH:mm:ss"
- `theaterFundCurrentAmount/targetAmount`: 0.00-999999.99

### 数据库表结构
```sql
CREATE TABLE `t_user_reminder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `salary_day` int(2) DEFAULT NULL,
  `show_time` timestamp NULL DEFAULT NULL,
  `ticket_sale_time` timestamp NULL DEFAULT NULL,
  `theater_fund_current_amount` decimal(10,2) DEFAULT '0.00',
  `theater_fund_target_amount` decimal(10,2) DEFAULT '0.00',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
);
```

### 错误处理
- `200`: 操作成功
- `500`: 服务器错误
- 具体错误信息在 `msg` 字段中返回

---

## 🚀 快速开始

1. **获取设置**：页面加载时调用获取接口
2. **保存设置**：表单提交时调用保存接口
3. **实时更新**：看剧基金可以单独更新

这份简化版API文档去除了复杂的开关设置，专注于核心的提醒时间和金额管理功能。
