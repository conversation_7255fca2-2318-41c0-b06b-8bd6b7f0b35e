# 用户提醒功能实现总结

## 功能概述

为前端用户提供四种类型的提醒设置功能：

1. **发薪日提醒** - 用户可以设置发薪日期和提前提醒天数
2. **开演时间提醒** - 用户可以设置开演前提醒的小时数  
3. **开票时间提醒** - 用户可以设置开票前提醒的小时数
4. **看剧基金提醒** - 用户可以设置当前金额、目标金额和提醒阈值

## 实现的文件结构

### 1. 数据库层
- **SQL脚本**: `docs/sql/user_reminder.sql`
  - 创建 `t_user_reminder` 表
  - 包含示例数据插入语句

### 2. 实体层 (Entity)
- **UserReminder.java**: `template-common-service/src/main/java/com/youying/common/core/domain/entity/UserReminder.java`
  - 用户提醒设置实体类
  - 使用MyBatis-Plus注解
  - 包含所有四种提醒类型的字段

### 3. 数据访问层 (Mapper)
- **UserReminderMapper.java**: `template-server-service/src/main/java/com/youying/system/mapper/UserReminderMapper.java`
  - 继承BaseMapper，提供基础CRUD操作
  - 自定义方法：根据用户ID查询和删除
- **UserReminderMapper.xml**: `template-server-service/src/main/resources/mapper/system/UserReminderMapper.xml`
  - MyBatis映射文件
  - 定义SQL查询语句

### 4. 业务逻辑层 (Service)
- **UserReminderService.java**: `template-server-service/src/main/java/com/youying/system/service/UserReminderService.java`
  - 服务接口定义
- **UserReminderServiceImpl.java**: `template-server-service/src/main/java/com/youying/system/service/impl/UserReminderServiceImpl.java`
  - 服务实现类
  - 包含业务逻辑：查询、保存更新、删除、初始化默认设置

### 5. 控制器层 (Controller)
- **UserReminderController.java**: `template-api-service/src/main/java/com/youying/web/controller/UserReminderController.java`
  - REST API控制器
  - 提供4个主要接口

### 6. 数据传输对象 (DTO)
- **UserReminderRequest.java**: `template-server-service/src/main/java/com/youying/system/domain/userreminder/UserReminderRequest.java`
  - 请求DTO，包含参数验证注解
- **UserReminderResponse.java**: `template-server-service/src/main/java/com/youying/system/domain/userreminder/UserReminderResponse.java`
  - 响应DTO，结构化返回数据

## API接口

### 1. 获取用户提醒设置
- **URL**: `GET /userReminder/getUserReminder`
- **功能**: 获取当前用户的提醒设置，如果不存在则返回默认设置

### 2. 保存或更新用户提醒设置
- **URL**: `POST /userReminder/saveOrUpdateUserReminder`
- **功能**: 保存或更新用户的提醒设置
- **参数验证**: 包含完整的参数验证规则

### 3. 删除用户提醒设置
- **URL**: `DELETE /userReminder/deleteUserReminder`
- **功能**: 软删除用户的提醒设置

### 4. 更新看剧基金金额
- **URL**: `POST /userReminder/updateTheaterFund`
- **功能**: 单独更新看剧基金的当前金额和目标金额

## 技术特点

### 1. 数据库设计
- 使用单表存储所有提醒设置
- 每个用户一条记录（通过user_id唯一约束）
- 支持软删除机制
- 包含完整的审计字段（创建时间、更新时间、创建者、更新者）

### 2. 业务逻辑
- **默认设置**: 首次访问时自动返回默认配置
- **智能保存**: 自动判断是新增还是更新操作
- **进度计算**: 自动计算看剧基金的完成进度和阈值状态
- **参数验证**: 完整的输入参数验证规则

### 3. 响应结构
- 使用结构化的响应DTO，将相关字段分组
- 看剧基金部分包含计算字段（进度百分比、是否达到阈值）
- 统一的错误处理和响应格式

### 4. 安全性
- 所有接口都需要用户登录
- 通过BaseController自动获取当前用户ID
- 用户只能操作自己的提醒设置

## 使用示例

### 前端调用示例

```javascript
// 获取用户提醒设置
fetch('/userReminder/getUserReminder', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token
  }
})

// 保存提醒设置
fetch('/userReminder/saveOrUpdateUserReminder', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    salaryDayEnabled: 1,
    salaryDay: 15,
    salaryDayAdvanceDays: 1,
    showTimeEnabled: 1,
    showTimeAdvanceHours: 2,
    ticketSaleEnabled: 1,
    ticketSaleAdvanceHours: 24,
    theaterFundEnabled: 1,
    theaterFundCurrentAmount: 500.00,
    theaterFundTargetAmount: 1000.00,
    theaterFundReminderThreshold: 0.80
  })
})
```

## 部署说明

1. **数据库**: 执行 `docs/sql/user_reminder.sql` 创建表结构
2. **代码**: 所有代码文件已按照项目规范放置在正确位置
3. **依赖**: 使用项目现有依赖，无需额外添加
4. **测试**: 建议编写单元测试验证功能正确性

## 扩展建议

1. **定时任务**: 可以添加定时任务来实际发送提醒通知
2. **消息推送**: 集成微信小程序消息推送或短信通知
3. **提醒历史**: 添加提醒发送历史记录表
4. **个性化设置**: 支持更多个性化的提醒设置选项
