# 用户提醒功能API文档

## 概述

用户提醒功能为前端用户提供四种类型的提醒设置：
1. **发薪日提醒** - 用户可以设置发薪日期和提前提醒天数
2. **开演时间提醒** - 用户可以设置开演前提醒的小时数
3. **开票时间提醒** - 用户可以设置开票前提醒的小时数
4. **看剧基金提醒** - 用户可以设置当前金额、目标金额和提醒阈值

## API接口

### 1. 获取用户提醒设置

**接口地址：** `GET /userReminder/getUserReminder`

**请求参数：** 无（从登录用户信息中获取userId）

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "userId": 123,
    "salaryDayReminder": {
      "enabled": 1,
      "salaryDay": 15,
      "advanceDays": 1
    },
    "showTimeReminder": {
      "enabled": 1,
      "advanceHours": 2
    },
    "ticketSaleReminder": {
      "enabled": 1,
      "advanceHours": 24
    },
    "theaterFundReminder": {
      "enabled": 1,
      "currentAmount": 500.00,
      "targetAmount": 1000.00,
      "reminderThreshold": 0.80,
      "progressPercentage": 50.00,
      "reachedThreshold": false
    },
    "status": 1,
    "createTime": "2025-01-03 10:00:00",
    "updateTime": "2025-01-03 10:00:00",
    "remark": "用户提醒设置"
  }
}
```

### 2. 保存或更新用户提醒设置

**接口地址：** `POST /userReminder/saveOrUpdateUserReminder`

**请求参数：**
```json
{
  "salaryDayEnabled": 1,
  "salaryDay": 15,
  "salaryDayAdvanceDays": 1,
  "showTimeEnabled": 1,
  "showTimeAdvanceHours": 2,
  "ticketSaleEnabled": 1,
  "ticketSaleAdvanceHours": 24,
  "theaterFundEnabled": 1,
  "theaterFundCurrentAmount": 500.00,
  "theaterFundTargetAmount": 1000.00,
  "theaterFundReminderThreshold": 0.80,
  "remark": "更新提醒设置"
}
```

**参数说明：**
- `salaryDayEnabled`: 发薪日提醒是否启用 (0:关闭, 1:开启)
- `salaryDay`: 发薪日 (1-31)
- `salaryDayAdvanceDays`: 发薪日提前提醒天数 (0-30)
- `showTimeEnabled`: 开演时间提醒是否启用 (0:关闭, 1:开启)
- `showTimeAdvanceHours`: 开演时间提前提醒小时数 (0-168)
- `ticketSaleEnabled`: 开票时间提醒是否启用 (0:关闭, 1:开启)
- `ticketSaleAdvanceHours`: 开票时间提前提醒小时数 (0-720)
- `theaterFundEnabled`: 看剧基金提醒是否启用 (0:关闭, 1:开启)
- `theaterFundCurrentAmount`: 看剧基金当前金额 (0.00-999999.99)
- `theaterFundTargetAmount`: 看剧基金目标金额 (0.00-999999.99)
- `theaterFundReminderThreshold`: 看剧基金提醒阈值 (0.10-1.00)

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 3. 删除用户提醒设置

**接口地址：** `DELETE /userReminder/deleteUserReminder`

**请求参数：** 无（从登录用户信息中获取userId）

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4. 更新看剧基金金额

**接口地址：** `POST /userReminder/updateTheaterFund`

**请求参数：**
- `currentAmount`: 当前金额（必填）
- `targetAmount`: 目标金额（可选）

**示例：** `POST /userReminder/updateTheaterFund?currentAmount=600.00&targetAmount=1200.00`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

## 数据库表结构

### t_user_reminder 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| user_id | bigint(20) | 用户ID |
| salary_day_enabled | tinyint(1) | 发薪日提醒是否启用 |
| salary_day | int(2) | 发薪日(1-31) |
| salary_day_advance_days | int(2) | 发薪日提前提醒天数 |
| show_time_enabled | tinyint(1) | 开演时间提醒是否启用 |
| show_time_advance_hours | int(3) | 开演时间提前提醒小时数 |
| ticket_sale_enabled | tinyint(1) | 开票时间提醒是否启用 |
| ticket_sale_advance_hours | int(3) | 开票时间提前提醒小时数 |
| theater_fund_enabled | tinyint(1) | 看剧基金提醒是否启用 |
| theater_fund_current_amount | decimal(10,2) | 看剧基金当前金额 |
| theater_fund_target_amount | decimal(10,2) | 看剧基金目标金额 |
| theater_fund_reminder_threshold | decimal(3,2) | 看剧基金提醒阈值 |
| status | tinyint(1) | 状态(0:禁用,1:启用) |
| deleted | tinyint(1) | 是否删除(1:未删除,0:已删除) |
| create_by | varchar(64) | 创建者 |
| create_time | datetime | 创建时间 |
| update_by | varchar(64) | 更新者 |
| update_time | datetime | 更新时间 |
| remark | varchar(500) | 备注 |

## 使用说明

1. **首次使用**：用户首次调用获取接口时，如果没有设置记录，系统会返回默认设置
2. **数据验证**：所有输入参数都有相应的验证规则，确保数据的有效性
3. **权限控制**：所有接口都需要用户登录，通过BaseController获取当前用户ID
4. **看剧基金进度**：系统会自动计算当前进度百分比和是否达到提醒阈值
5. **软删除**：删除操作采用软删除方式，不会物理删除数据

## 错误码说明

- `200`: 操作成功
- `500`: 操作失败
- 具体错误信息会在`msg`字段中返回
