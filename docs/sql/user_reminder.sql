-- 用户提醒设置表
CREATE TABLE `t_user_reminder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  
  -- 发薪日提醒设置
  `salary_day_enabled` tinyint(1) DEFAULT '0' COMMENT '发薪日提醒是否启用(0:关闭,1:开启)',
  `salary_day` int(2) DEFAULT NULL COMMENT '发薪日(1-31)',
  `salary_day_advance_days` int(2) DEFAULT '0' COMMENT '发薪日提前提醒天数',
  
  -- 开演时间提醒设置
  `show_time_enabled` tinyint(1) DEFAULT '0' COMMENT '开演时间提醒是否启用(0:关闭,1:开启)',
  `show_time_advance_hours` int(3) DEFAULT '2' COMMENT '开演时间提前提醒小时数',
  
  -- 开票时间提醒设置
  `ticket_sale_enabled` tinyint(1) DEFAULT '0' COMMENT '开票时间提醒是否启用(0:关闭,1:开启)',
  `ticket_sale_advance_hours` int(3) DEFAULT '24' COMMENT '开票时间提前提醒小时数',
  
  -- 看剧基金提醒设置
  `theater_fund_enabled` tinyint(1) DEFAULT '0' COMMENT '看剧基金提醒是否启用(0:关闭,1:开启)',
  `theater_fund_current_amount` decimal(10,2) DEFAULT '0.00' COMMENT '看剧基金当前金额',
  `theater_fund_target_amount` decimal(10,2) DEFAULT '0.00' COMMENT '看剧基金目标金额',
  `theater_fund_reminder_threshold` decimal(3,2) DEFAULT '0.80' COMMENT '看剧基金提醒阈值(0.8表示80%)',
  
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `deleted` tinyint(1) DEFAULT '1' COMMENT '是否删除(1:未删除,0:已删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提醒设置表';

-- 插入示例数据
INSERT INTO `t_user_reminder` (
  `user_id`, 
  `salary_day_enabled`, `salary_day`, `salary_day_advance_days`,
  `show_time_enabled`, `show_time_advance_hours`,
  `ticket_sale_enabled`, `ticket_sale_advance_hours`,
  `theater_fund_enabled`, `theater_fund_current_amount`, `theater_fund_target_amount`, `theater_fund_reminder_threshold`,
  `status`, `deleted`, `create_by`, `update_by`, `remark`
) VALUES (
  1, -- 假设用户ID为1
  1, 15, 1, -- 发薪日提醒：启用，15号发薪，提前1天提醒
  1, 2, -- 开演时间提醒：启用，提前2小时提醒
  1, 24, -- 开票时间提醒：启用，提前24小时提醒
  1, 500.00, 1000.00, 0.80, -- 看剧基金提醒：启用，当前500元，目标1000元，80%阈值提醒
  1, 1, 'system', 'system', '示例用户提醒设置'
);
