-- 用户提醒设置表（简化版）
CREATE TABLE `t_user_reminder` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',

  -- 发薪日提醒设置
  `salary_day` int(2) DEFAULT NULL COMMENT '发薪日(1-31)',

  -- 开演时间提醒设置
  `show_time` timestamp NULL DEFAULT NULL COMMENT '开演时间',

  -- 开票时间提醒设置
  `ticket_sale_time` timestamp NULL DEFAULT NULL COMMENT '开票时间',

  -- 看剧基金设置
  `theater_fund_current_amount` decimal(10,2) DEFAULT '0.00' COMMENT '看剧基金当前金额',
  `theater_fund_target_amount` decimal(10,2) DEFAULT '0.00' COMMENT '看剧基金目标金额',

  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提醒设置表';

-- 插入示例数据
INSERT INTO `t_user_reminder` (
  `user_id`,
  `salary_day`,
  `show_time`,
  `ticket_sale_time`,
  `theater_fund_current_amount`,
  `theater_fund_target_amount`
) VALUES (
  1, -- 假设用户ID为1
  15, -- 15号发薪
  '2025-02-15 19:30:00', -- 开演时间示例
  '2025-01-15 10:00:00', -- 开票时间示例
  500.00, -- 当前金额500元
  1000.00 -- 目标金额1000元
);
