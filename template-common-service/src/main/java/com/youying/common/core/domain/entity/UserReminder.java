package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户提醒设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_reminder")
public class UserReminder extends Model<UserReminder> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 发薪日提醒是否启用(0:关闭,1:开启)
     */
    @TableField("salary_day_enabled")
    private Integer salaryDayEnabled;

    /**
     * 发薪日(1-31)
     */
    @TableField("salary_day")
    private Integer salaryDay;

    /**
     * 发薪日提前提醒天数
     */
    @TableField("salary_day_advance_days")
    private Integer salaryDayAdvanceDays;

    /**
     * 开演时间提醒是否启用(0:关闭,1:开启)
     */
    @TableField("show_time_enabled")
    private Integer showTimeEnabled;

    /**
     * 开演时间提前提醒小时数
     */
    @TableField("show_time_advance_hours")
    private Integer showTimeAdvanceHours;

    /**
     * 开票时间提醒是否启用(0:关闭,1:开启)
     */
    @TableField("ticket_sale_enabled")
    private Integer ticketSaleEnabled;

    /**
     * 开票时间提前提醒小时数
     */
    @TableField("ticket_sale_advance_hours")
    private Integer ticketSaleAdvanceHours;

    /**
     * 看剧基金提醒是否启用(0:关闭,1:开启)
     */
    @TableField("theater_fund_enabled")
    private Integer theaterFundEnabled;

    /**
     * 看剧基金当前金额
     */
    @TableField("theater_fund_current_amount")
    private BigDecimal theaterFundCurrentAmount;

    /**
     * 看剧基金目标金额
     */
    @TableField("theater_fund_target_amount")
    private BigDecimal theaterFundTargetAmount;

    /**
     * 看剧基金提醒阈值(0.8表示80%)
     */
    @TableField("theater_fund_reminder_threshold")
    private BigDecimal theaterFundReminderThreshold;

    /**
     * 状态(0:禁用,1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除(1:未删除,0:已删除)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
