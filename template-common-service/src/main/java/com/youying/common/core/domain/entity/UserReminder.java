package com.youying.common.core.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户提醒设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_reminder")
public class UserReminder extends Model<UserReminder> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 发薪日(1-31)
     */
    @TableField("salary_day")
    private Integer salaryDay;

    /**
     * 开演时间
     */
    @TableField("show_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 开票时间
     */
    @TableField("ticket_sale_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ticketSaleTime;

    /**
     * 看剧基金当前金额
     */
    @TableField("theater_fund_current_amount")
    private BigDecimal theaterFundCurrentAmount;

    /**
     * 看剧基金目标金额
     */
    @TableField("theater_fund_target_amount")
    private BigDecimal theaterFundTargetAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
