package com.youying.web.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.validation.Valid;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.domain.userreminder.UserReminderRequest;
import com.youying.system.domain.userreminder.UserReminderResponse;
import com.youying.system.service.UserReminderService;

/**
 * 用户提醒设置控制器
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestController
@RequestMapping("/userReminder")
public class UserReminderController extends BaseController<UserReminder> {

    @Autowired
    private UserReminderService userReminderService;

    /**
     * 获取用户提醒设置
     *
     * @return 用户提醒设置
     */
    @GetMapping("/getUserReminder")
    public R<UserReminderResponse> getUserReminder() {
        Long userId = getUserId();
        UserReminder userReminder = userReminderService.findByUserId(userId);
        UserReminderResponse response = convertToResponse(userReminder);
        return R.ok(response);
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param request 用户提醒设置请求
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdateUserReminder")
    public R<Void> saveOrUpdateUserReminder(@Valid @RequestBody UserReminderRequest request) {
        Long userId = getUserId();
        UserReminder userReminder = convertToEntity(request, userId);

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("保存用户提醒设置失败");
        }
    }

    /**
     * 删除用户提醒设置
     *
     * @return 操作结果
     */
    @DeleteMapping("/deleteUserReminder")
    public R<Void> deleteUserReminder() {
        Long userId = getUserId();
        boolean success = userReminderService.deleteByUserId(userId);
        if (success) {
            return R.ok();
        } else {
            return R.fail("删除用户提醒设置失败");
        }
    }

    /**
     * 更新看剧基金金额
     *
     * @param currentAmount 当前金额
     * @param targetAmount  目标金额
     * @return 操作结果
     */
    @PostMapping("/updateTheaterFund")
    public R<Void> updateTheaterFund(@RequestParam BigDecimal currentAmount,
            @RequestParam(required = false) BigDecimal targetAmount) {
        Long userId = getUserId();
        UserReminder userReminder = userReminderService.findByUserId(userId);

        if (userReminder == null) {
            userReminder = userReminderService.initUserReminder(userId);
        }

        userReminder.setTheaterFundCurrentAmount(currentAmount);
        if (targetAmount != null) {
            userReminder.setTheaterFundTargetAmount(targetAmount);
        }

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("更新看剧基金金额失败");
        }
    }

    /**
     * 将实体转换为响应DTO
     *
     * @param userReminder 用户提醒设置实体
     * @return 响应DTO
     */
    private UserReminderResponse convertToResponse(UserReminder userReminder) {
        UserReminderResponse response = new UserReminderResponse();
        BeanUtils.copyProperties(userReminder, response);

        // 发薪日提醒设置
        UserReminderResponse.SalaryDayReminder salaryDayReminder = new UserReminderResponse.SalaryDayReminder();
        salaryDayReminder.setEnabled(userReminder.getSalaryDayEnabled());
        salaryDayReminder.setSalaryDay(userReminder.getSalaryDay());
        salaryDayReminder.setAdvanceDays(userReminder.getSalaryDayAdvanceDays());
        response.setSalaryDayReminder(salaryDayReminder);

        // 开演时间提醒设置
        UserReminderResponse.ShowTimeReminder showTimeReminder = new UserReminderResponse.ShowTimeReminder();
        showTimeReminder.setEnabled(userReminder.getShowTimeEnabled());
        showTimeReminder.setAdvanceHours(userReminder.getShowTimeAdvanceHours());
        response.setShowTimeReminder(showTimeReminder);

        // 开票时间提醒设置
        UserReminderResponse.TicketSaleReminder ticketSaleReminder = new UserReminderResponse.TicketSaleReminder();
        ticketSaleReminder.setEnabled(userReminder.getTicketSaleEnabled());
        ticketSaleReminder.setAdvanceHours(userReminder.getTicketSaleAdvanceHours());
        response.setTicketSaleReminder(ticketSaleReminder);

        // 看剧基金提醒设置
        UserReminderResponse.TheaterFundReminder theaterFundReminder = new UserReminderResponse.TheaterFundReminder();
        theaterFundReminder.setEnabled(userReminder.getTheaterFundEnabled());
        theaterFundReminder.setCurrentAmount(userReminder.getTheaterFundCurrentAmount());
        theaterFundReminder.setTargetAmount(userReminder.getTheaterFundTargetAmount());
        theaterFundReminder.setReminderThreshold(userReminder.getTheaterFundReminderThreshold());

        // 计算进度百分比和是否达到阈值
        if (userReminder.getTheaterFundTargetAmount() != null &&
                userReminder.getTheaterFundTargetAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal progress = userReminder.getTheaterFundCurrentAmount()
                    .divide(userReminder.getTheaterFundTargetAmount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            theaterFundReminder.setProgressPercentage(progress);
            theaterFundReminder.setReachedThreshold(progress.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)
                    .compareTo(userReminder.getTheaterFundReminderThreshold()) >= 0);
        } else {
            theaterFundReminder.setProgressPercentage(BigDecimal.ZERO);
            theaterFundReminder.setReachedThreshold(false);
        }

        response.setTheaterFundReminder(theaterFundReminder);

        return response;
    }

    /**
     * 将请求DTO转换为实体
     *
     * @param request 请求DTO
     * @param userId  用户ID
     * @return 实体
     */
    private UserReminder convertToEntity(UserReminderRequest request, Long userId) {
        UserReminder userReminder = new UserReminder();
        userReminder.setUserId(userId);

        // 发薪日提醒设置
        userReminder.setSalaryDayEnabled(request.getSalaryDayEnabled());
        userReminder.setSalaryDay(request.getSalaryDay());
        userReminder.setSalaryDayAdvanceDays(request.getSalaryDayAdvanceDays());

        // 开演时间提醒设置
        userReminder.setShowTimeEnabled(request.getShowTimeEnabled());
        userReminder.setShowTimeAdvanceHours(request.getShowTimeAdvanceHours());

        // 开票时间提醒设置
        userReminder.setTicketSaleEnabled(request.getTicketSaleEnabled());
        userReminder.setTicketSaleAdvanceHours(request.getTicketSaleAdvanceHours());

        // 看剧基金提醒设置
        userReminder.setTheaterFundEnabled(request.getTheaterFundEnabled());
        userReminder.setTheaterFundCurrentAmount(request.getTheaterFundCurrentAmount());
        userReminder.setTheaterFundTargetAmount(request.getTheaterFundTargetAmount());
        userReminder.setTheaterFundReminderThreshold(request.getTheaterFundReminderThreshold());

        userReminder.setRemark(request.getRemark());
        userReminder.setCreateBy(getUsername());
        userReminder.setUpdateBy(getUsername());

        return userReminder;
    }
}
