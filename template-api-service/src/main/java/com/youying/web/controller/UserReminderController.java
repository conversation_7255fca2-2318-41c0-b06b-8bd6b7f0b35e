package com.youying.web.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.validation.Valid;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserReminder;
import com.youying.system.domain.userreminder.UserReminderRequest;
import com.youying.system.domain.userreminder.UserReminderResponse;
import com.youying.system.service.UserReminderService;

/**
 * 用户提醒设置控制器
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@RestController
@RequestMapping("/userReminder")
public class UserReminderController extends BaseController<UserReminder> {

    @Autowired
    private UserReminderService userReminderService;

    /**
     * 获取用户提醒设置
     *
     * @return 用户提醒设置
     */
    @GetMapping("/getUserReminder")
    public R<UserReminderResponse> getUserReminder() {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = userReminderService.findByUserId(userId);
        UserReminderResponse response = convertToResponse(userReminder);
        return R.ok(response);
    }

    /**
     * 保存或更新用户提醒设置
     *
     * @param request 用户提醒设置请求
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdateUserReminder")
    public R<Void> saveOrUpdateUserReminder(@Valid @RequestBody UserReminderRequest request) {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = convertToEntity(request, userId);

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("保存用户提醒设置失败");
        }
    }

    /**
     * 删除用户提醒设置
     *
     * @return 操作结果
     */
    @DeleteMapping("/deleteUserReminder")
    public R<Void> deleteUserReminder() {
        Integer userId = getUserId().intValue();
        boolean success = userReminderService.removeById(userId);
        if (success) {
            return R.ok();
        } else {
            return R.fail("删除用户提醒设置失败");
        }
    }

    /**
     * 更新看剧基金金额
     *
     * @param currentAmount 当前金额
     * @param targetAmount  目标金额
     * @return 操作结果
     */
    @PostMapping("/updateTheaterFund")
    public R<Void> updateTheaterFund(@RequestParam BigDecimal currentAmount,
            @RequestParam(required = false) BigDecimal targetAmount) {
        Integer userId = getUserId().intValue();
        UserReminder userReminder = userReminderService.findByUserId(userId);

        if (userReminder.getId() == null) {
            // 如果不存在记录，创建新记录
            userReminder.setUserId(userId);
        }

        userReminder.setTheaterFundCurrentAmount(currentAmount);
        if (targetAmount != null) {
            userReminder.setTheaterFundTargetAmount(targetAmount);
        }

        boolean success = userReminderService.saveOrUpdateUserReminder(userReminder);
        if (success) {
            return R.ok();
        } else {
            return R.fail("更新看剧基金金额失败");
        }
    }

    /**
     * 将实体转换为响应DTO
     *
     * @param userReminder 用户提醒设置实体
     * @return 响应DTO
     */
    private UserReminderResponse convertToResponse(UserReminder userReminder) {
        UserReminderResponse response = new UserReminderResponse();
        BeanUtils.copyProperties(userReminder, response);

        // 计算看剧基金进度百分比
        if (userReminder.getTheaterFundTargetAmount() != null &&
                userReminder.getTheaterFundTargetAmount().compareTo(BigDecimal.ZERO) > 0 &&
                userReminder.getTheaterFundCurrentAmount() != null) {
            BigDecimal progress = userReminder.getTheaterFundCurrentAmount()
                    .divide(userReminder.getTheaterFundTargetAmount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            response.setProgressPercentage(progress);
        } else {
            response.setProgressPercentage(BigDecimal.ZERO);
        }

        return response;
    }

    /**
     * 将请求DTO转换为实体
     *
     * @param request 请求DTO
     * @param userId  用户ID
     * @return 实体
     */
    private UserReminder convertToEntity(UserReminderRequest request, Integer userId) {
        UserReminder userReminder = new UserReminder();
        userReminder.setUserId(userId);
        userReminder.setSalaryDay(request.getSalaryDay());
        userReminder.setShowTime(request.getShowTime());
        userReminder.setTicketSaleTime(request.getTicketSaleTime());
        userReminder.setTheaterFundCurrentAmount(request.getTheaterFundCurrentAmount());
        userReminder.setTheaterFundTargetAmount(request.getTheaterFundTargetAmount());

        return userReminder;
    }
}
